/**
 * Modern Component System with Web Components
 * Utiliza recursos modernos do JavaScript para criar componentes reutilizáveis
 */

// 1. LAZY LOADING AVANÇADO
class LazyLoader {
  constructor() {
    this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {
      rootMargin: '50px',
      threshold: 0.1
    });
  }

  observe(element) {
    this.observer.observe(element);
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.loadContent(entry.target);
        this.observer.unobserve(entry.target);
      }
    });
  }

  async loadContent(element) {
    const src = element.dataset.src;
    if (src) {
      try {
        element.src = src;
        element.classList.add('loaded');
      } catch (error) {
        console.error('Erro ao carregar conteúdo:', error);
      }
    }
  }
}

// 2. SISTEMA DE NOTIFICAÇÕES MODERNO
class NotificationSystem {
  constructor() {
    this.container = this.createContainer();
    this.notifications = new Map();
  }

  createContainer() {
    const container = document.createElement('div');
    container.className = 'fixed top-4 right-4 z-[9999] space-y-2';
    container.id = 'notification-container';
    document.body.appendChild(container);
    return container;
  }

  show(message, type = 'info', duration = 5000) {
    const id = Date.now().toString();
    const notification = this.createNotification(message, type, id);
    
    this.container.appendChild(notification);
    this.notifications.set(id, notification);

    // Animação de entrada
    requestAnimationFrame(() => {
      notification.classList.add('translate-x-0', 'opacity-100');
    });

    // Auto-remove
    if (duration > 0) {
      setTimeout(() => this.remove(id), duration);
    }

    return id;
  }

  createNotification(message, type, id) {
    const notification = document.createElement('div');
    notification.className = `
      transform translate-x-full opacity-0 transition-all duration-300 ease-in-out
      max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto
      ring-1 ring-black ring-opacity-5 overflow-hidden
    `;
    
    const colors = {
      success: 'text-green-500',
      error: 'text-red-500',
      warning: 'text-yellow-500',
      info: 'text-blue-500'
    };

    const icons = {
      success: 'fas fa-check-circle',
      error: 'fas fa-exclamation-circle',
      warning: 'fas fa-exclamation-triangle',
      info: 'fas fa-info-circle'
    };

    notification.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <i class="${icons[type]} ${colors[type]}"></i>
          </div>
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              ${message}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button onclick="notificationSystem.remove('${id}')" 
                    class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    `;

    return notification;
  }

  remove(id) {
    const notification = this.notifications.get(id);
    if (notification) {
      notification.classList.add('translate-x-full', 'opacity-0');
      setTimeout(() => {
        notification.remove();
        this.notifications.delete(id);
      }, 300);
    }
  }
}

// 3. GERENCIADOR DE ESTADO REATIVO
class StateManager {
  constructor(initialState = {}) {
    this.state = new Proxy(initialState, {
      set: (target, property, value) => {
        const oldValue = target[property];
        target[property] = value;
        this.notifySubscribers(property, value, oldValue);
        return true;
      }
    });
    
    this.subscribers = new Map();
  }

  subscribe(property, callback) {
    if (!this.subscribers.has(property)) {
      this.subscribers.set(property, new Set());
    }
    this.subscribers.get(property).add(callback);

    // Retorna função para unsubscribe
    return () => {
      this.subscribers.get(property)?.delete(callback);
    };
  }

  notifySubscribers(property, newValue, oldValue) {
    const callbacks = this.subscribers.get(property);
    if (callbacks) {
      callbacks.forEach(callback => callback(newValue, oldValue));
    }
  }

  setState(updates) {
    Object.assign(this.state, updates);
  }

  getState() {
    return { ...this.state };
  }
}

// 4. COMPONENTE DE LOADING AVANÇADO
class LoadingManager {
  constructor() {
    this.activeLoaders = new Set();
    this.createGlobalLoader();
  }

  createGlobalLoader() {
    const loader = document.createElement('div');
    loader.id = 'global-loader';
    loader.className = `
      fixed inset-0 bg-black bg-opacity-50 z-[9998] 
      flex items-center justify-center opacity-0 
      pointer-events-none transition-opacity duration-300
    `;
    
    loader.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl">
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span class="text-gray-700 dark:text-gray-300">Carregando...</span>
        </div>
      </div>
    `;
    
    document.body.appendChild(loader);
    this.globalLoader = loader;
  }

  show(id = 'default') {
    this.activeLoaders.add(id);
    this.updateGlobalLoader();
  }

  hide(id = 'default') {
    this.activeLoaders.delete(id);
    this.updateGlobalLoader();
  }

  updateGlobalLoader() {
    if (this.activeLoaders.size > 0) {
      this.globalLoader.classList.add('opacity-100');
      this.globalLoader.classList.remove('pointer-events-none');
    } else {
      this.globalLoader.classList.remove('opacity-100');
      this.globalLoader.classList.add('pointer-events-none');
    }
  }
}

// 5. SISTEMA DE CACHE INTELIGENTE
class CacheManager {
  constructor(maxSize = 50) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessOrder = [];
  }

  set(key, value, ttl = 300000) { // 5 minutos default
    // Remove item mais antigo se cache estiver cheio
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      const oldestKey = this.accessOrder.shift();
      this.cache.delete(oldestKey);
    }

    const item = {
      value,
      timestamp: Date.now(),
      ttl
    };

    this.cache.set(key, item);
    this.updateAccessOrder(key);
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    // Verifica se expirou
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      this.removeFromAccessOrder(key);
      return null;
    }

    this.updateAccessOrder(key);
    return item.value;
  }

  updateAccessOrder(key) {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  removeFromAccessOrder(key) {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  clear() {
    this.cache.clear();
    this.accessOrder = [];
  }
}

// Inicialização global
const lazyLoader = new LazyLoader();
const notificationSystem = new NotificationSystem();
const stateManager = new StateManager({
  theme: 'dark',
  language: 'pt-BR',
  user: null
});
const loadingManager = new LoadingManager();
const cacheManager = new CacheManager();

// Exporta para uso global
window.ModernComponents = {
  LazyLoader,
  NotificationSystem,
  StateManager,
  LoadingManager,
  CacheManager,
  instances: {
    lazyLoader,
    notificationSystem,
    stateManager,
    loadingManager,
    cacheManager
  }
};
