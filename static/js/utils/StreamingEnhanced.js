/**
 * StreamingManager Aprimorado
 * Versão melhorada com recursos modernos de JavaScript, TailwindCSS e Flowbite
 */

class StreamingEnhanced extends StreamingManager {
  constructor() {
    super();
    this.virtualScroller = null;
    this.searchDebouncer = null;
    this.filterState = {
      genre: 'all',
      duration: 'all',
      popularity: 'all'
    };
    this.sortState = {
      field: 'date',
      direction: 'desc'
    };
  }

  /**
   * Setup aprimorado com recursos modernos
   */
  async setup() {
    await super.setup();
    this.initVirtualScrolling();
    this.initAdvancedSearch();
    this.initSmartFilters();
    this.initPlaylistPreview();
    this.initKeyboardNavigation();
    this.initAnalytics();
  }

  /**
   * Virtual Scrolling para performance com muitas playlists
   */
  initVirtualScrolling() {
    this.virtualScroller = {
      itemHeight: 280,
      containerHeight: 600,
      buffer: 5,
      visibleItems: [],
      
      render: (items, container) => {
        const visibleCount = Math.ceil(this.virtualScroller.containerHeight / this.virtualScroller.itemHeight);
        const startIndex = Math.max(0, this.currentPage * this.itemsPerPage - this.virtualScroller.buffer);
        const endIndex = Math.min(items.length, startIndex + visibleCount + this.virtualScroller.buffer * 2);
        
        container.innerHTML = '';
        container.style.height = `${items.length * this.virtualScroller.itemHeight}px`;
        
        for (let i = startIndex; i < endIndex; i++) {
          const item = items[i];
          if (item) {
            const element = this.createEnhancedPlaylistCard(item, i);
            element.style.position = 'absolute';
            element.style.top = `${i * this.virtualScroller.itemHeight}px`;
            element.style.width = '100%';
            container.appendChild(element);
          }
        }
      }
    };
  }

  /**
   * Busca avançada com debounce e sugestões
   */
  initAdvancedSearch() {
    const searchContainers = document.querySelectorAll('.search-section');
    
    searchContainers.forEach(container => {
      const input = container.querySelector('input[type="search"]');
      const suggestionsContainer = this.createSuggestionsContainer(container);
      
      input.addEventListener('input', (e) => {
        clearTimeout(this.searchDebouncer);
        this.searchDebouncer = setTimeout(() => {
          this.performAdvancedSearch(e.target.value, suggestionsContainer);
        }, 300);
      });

      input.addEventListener('focus', () => {
        this.showSearchSuggestions(suggestionsContainer);
      });

      input.addEventListener('blur', () => {
        setTimeout(() => this.hideSearchSuggestions(suggestionsContainer), 200);
      });
    });
  }

  createSuggestionsContainer(parent) {
    const container = document.createElement('div');
    container.className = `
      absolute top-full left-0 right-0 z-50 bg-white dark:bg-gray-800 
      border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg 
      max-h-60 overflow-y-auto hidden
    `;
    parent.appendChild(container);
    return container;
  }

  async performAdvancedSearch(query, suggestionsContainer) {
    if (query.length < 2) {
      this.hideSearchSuggestions(suggestionsContainer);
      return;
    }

    // Busca local primeiro (mais rápido)
    const localResults = this.searchLocal(query);
    
    // Busca remota para sugestões
    try {
      const remoteResults = await this.searchRemote(query);
      this.displaySearchSuggestions([...localResults, ...remoteResults], suggestionsContainer);
    } catch (error) {
      this.displaySearchSuggestions(localResults, suggestionsContainer);
    }
  }

  searchLocal(query) {
    const currentData = this.currentTab === 'youtube' ? this.youtubeData : this.spotifyData;
    if (!currentData) return [];

    return currentData.filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5);
  }

  async searchRemote(query) {
    const endpoint = this.currentTab === 'youtube' ? '/api/search/youtube' : '/api/search/spotify';
    const response = await fetch(`${endpoint}?q=${encodeURIComponent(query)}&limit=5`);
    return response.json();
  }

  displaySearchSuggestions(results, container) {
    container.innerHTML = results.map(item => `
      <div class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
           onclick="streamingEnhanced.selectSuggestion('${item.id}')">
        <div class="flex items-center space-x-3">
          <img src="${item.thumbnail}" alt="${item.title}" class="w-10 h-10 rounded object-cover">
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">${item.title}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">${item.description}</p>
          </div>
          <i class="fas fa-search text-gray-400"></i>
        </div>
      </div>
    `).join('');
    
    container.classList.remove('hidden');
  }

  /**
   * Filtros inteligentes
   */
  initSmartFilters() {
    const filtersContainer = this.createFiltersContainer();
    this.setupFilterEvents(filtersContainer);
  }

  createFiltersContainer() {
    const container = document.createElement('div');
    container.className = 'filters-container mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg';
    container.innerHTML = `
      <div class="flex flex-wrap gap-4 items-center">
        <div class="filter-group">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">Gênero</label>
          <select data-filter="genre" class="filter-select">
            <option value="all">Todos</option>
            <option value="electronic">Eletrônica</option>
            <option value="ambient">Ambient</option>
            <option value="experimental">Experimental</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">Duração</label>
          <select data-filter="duration" class="filter-select">
            <option value="all">Todas</option>
            <option value="short">Curta (< 30min)</option>
            <option value="medium">Média (30-60min)</option>
            <option value="long">Longa (> 60min)</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">Ordenar por</label>
          <select data-sort="field" class="filter-select">
            <option value="date">Data</option>
            <option value="popularity">Popularidade</option>
            <option value="title">Título</option>
            <option value="duration">Duração</option>
          </select>
        </div>
        
        <button class="btn-secondary" onclick="streamingEnhanced.resetFilters()">
          <i class="fas fa-undo mr-2"></i>Limpar Filtros
        </button>
      </div>
    `;

    // Insere antes do container de playlists
    const streamingContent = document.querySelector('.streaming-content');
    const tabsContainer = streamingContent.querySelector('.tabs-container').parentElement;
    tabsContainer.appendChild(container);
    
    return container;
  }

  /**
   * Preview de playlist com hover
   */
  initPlaylistPreview() {
    this.previewModal = this.createPreviewModal();
  }

  createPreviewModal() {
    const modal = document.createElement('div');
    modal.id = 'playlist-preview-modal';
    modal.className = `
      fixed inset-0 bg-black bg-opacity-50 z-[9999] 
      flex items-center justify-center opacity-0 
      pointer-events-none transition-opacity duration-300
    `;
    
    modal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full mx-4 transform scale-95 transition-transform duration-300">
        <div class="p-6">
          <div class="flex justify-between items-start mb-4">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white" id="preview-title"></h3>
            <button onclick="streamingEnhanced.closePreview()" class="text-gray-400 hover:text-gray-600">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <img id="preview-thumbnail" src="" alt="" class="w-full h-48 object-cover rounded-lg">
              <div class="mt-4 space-y-2">
                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <i class="fas fa-clock mr-2"></i>
                  <span id="preview-duration"></span>
                </div>
                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <i class="fas fa-eye mr-2"></i>
                  <span id="preview-views"></span>
                </div>
              </div>
            </div>
            
            <div>
              <p class="text-gray-700 dark:text-gray-300 mb-4" id="preview-description"></p>
              <div class="space-y-3">
                <button class="btn-play w-full" onclick="streamingEnhanced.playPlaylist()">
                  <i class="fas fa-play mr-2"></i>Reproduzir Agora
                </button>
                <button class="btn-secondary w-full" onclick="streamingEnhanced.addToFavorites()">
                  <i class="fas fa-heart mr-2"></i>Adicionar aos Favoritos
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    return modal;
  }

  /**
   * Card de playlist aprimorado
   */
  createEnhancedPlaylistCard(playlist, index) {
    const card = document.createElement('div');
    card.className = `
      card-playlist group relative overflow-hidden
      transform transition-all duration-300 hover:scale-105
    `;
    
    card.innerHTML = `
      <div class="relative">
        <img data-src="${playlist.thumbnail}" 
             alt="${playlist.title}" 
             class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
             loading="lazy">
        
        <!-- Overlay com controles -->
        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
          <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 space-x-3">
            <button class="btn-play" onclick="streamingEnhanced.playPlaylist('${playlist.id}')">
              <i class="fas fa-play"></i>
            </button>
            <button class="btn-secondary" onclick="streamingEnhanced.showPreview('${playlist.id}')">
              <i class="fas fa-eye"></i>
            </button>
            <button class="btn-secondary" onclick="streamingEnhanced.addToFavorites('${playlist.id}')">
              <i class="fas fa-heart"></i>
            </button>
          </div>
        </div>
        
        <!-- Badge de duração -->
        <div class="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          ${playlist.duration}
        </div>
      </div>
      
      <div class="p-4">
        <h3 class="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
          ${playlist.title}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
          ${playlist.description}
        </p>
        
        <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span class="flex items-center">
            <i class="fas fa-eye mr-1"></i>
            ${this.formatNumber(playlist.views)}
          </span>
          <span class="flex items-center">
            <i class="fas fa-calendar mr-1"></i>
            ${this.formatDate(playlist.publishedAt)}
          </span>
        </div>
        
        <!-- Barra de progresso para playlists em reprodução -->
        <div class="mt-3 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-300" 
               style="width: ${playlist.progress || 0}%"></div>
        </div>
      </div>
    `;

    // Lazy loading
    const img = card.querySelector('img[data-src]');
    window.ModernComponents.instances.lazyLoader.observe(img);

    return card;
  }

  /**
   * Navegação por teclado
   */
  initKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      if (e.target.tagName === 'INPUT') return;
      
      switch(e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          this.navigatePlaylist('prev');
          break;
        case 'ArrowRight':
          e.preventDefault();
          this.navigatePlaylist('next');
          break;
        case ' ':
          e.preventDefault();
          this.togglePlayback();
          break;
        case 'Escape':
          this.closePreview();
          break;
      }
    });
  }

  /**
   * Analytics básico
   */
  initAnalytics() {
    this.analytics = {
      playlistViews: new Map(),
      searchQueries: [],
      userPreferences: {
        favoriteGenres: [],
        averageSessionTime: 0
      }
    };
  }

  // Métodos utilitários
  formatNumber(num) {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  }

  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric' 
    });
  }
}

// Inicialização
document.addEventListener('DOMContentLoaded', () => {
  window.streamingEnhanced = new StreamingEnhanced();
});
