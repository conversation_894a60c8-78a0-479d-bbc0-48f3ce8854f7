/**
 * Componentes Flowbite Aprimorados
 * Extensões e melhorias para componentes Flowbite
 */

class FlowbiteEnhanced {
  constructor() {
    this.initializeComponents();
  }

  initializeComponents() {
    this.initAdvancedModals();
    this.initSmartTooltips();
    this.initProgressiveCarousel();
    this.initInfiniteScroll();
    this.initAdvancedTabs();
  }

  // 1. MODAIS AVANÇADOS COM ANIMAÇÕES
  initAdvancedModals() {
    const modalTriggers = document.querySelectorAll('[data-modal-enhanced]');
    
    modalTriggers.forEach(trigger => {
      trigger.addEventListener('click', (e) => {
        e.preventDefault();
        const modalId = trigger.dataset.modalTarget;
        this.showEnhancedModal(modalId);
      });
    });
  }

  showEnhancedModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    // Cria backdrop customizado
    const backdrop = document.createElement('div');
    backdrop.className = `
      fixed inset-0 bg-black bg-opacity-50 z-[9999] 
      opacity-0 transition-opacity duration-300
    `;
    backdrop.id = `${modalId}-backdrop`;

    // Adiciona modal content
    modal.classList.add('z-[10000]', 'opacity-0', 'scale-95');
    modal.classList.remove('hidden');

    document.body.appendChild(backdrop);

    // Animação de entrada
    requestAnimationFrame(() => {
      backdrop.classList.add('opacity-100');
      modal.classList.add('opacity-100', 'scale-100');
      modal.classList.remove('scale-95');
    });

    // Event listeners
    backdrop.addEventListener('click', () => this.hideEnhancedModal(modalId));
    
    // ESC key
    const escHandler = (e) => {
      if (e.key === 'Escape') {
        this.hideEnhancedModal(modalId);
        document.removeEventListener('keydown', escHandler);
      }
    };
    document.addEventListener('keydown', escHandler);
  }

  hideEnhancedModal(modalId) {
    const modal = document.getElementById(modalId);
    const backdrop = document.getElementById(`${modalId}-backdrop`);
    
    if (!modal || !backdrop) return;

    // Animação de saída
    backdrop.classList.remove('opacity-100');
    modal.classList.remove('opacity-100', 'scale-100');
    modal.classList.add('scale-95');

    setTimeout(() => {
      modal.classList.add('hidden');
      backdrop.remove();
    }, 300);
  }

  // 2. TOOLTIPS INTELIGENTES
  initSmartTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip-smart]');
    
    tooltipElements.forEach(element => {
      const tooltip = this.createSmartTooltip(element);
      
      element.addEventListener('mouseenter', () => {
        this.showTooltip(element, tooltip);
      });
      
      element.addEventListener('mouseleave', () => {
        this.hideTooltip(tooltip);
      });
    });
  }

  createSmartTooltip(element) {
    const tooltip = document.createElement('div');
    tooltip.className = `
      absolute z-[9999] px-3 py-2 text-sm font-medium text-white 
      bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity 
      duration-300 pointer-events-none
    `;
    tooltip.textContent = element.dataset.tooltipSmart;
    
    // Arrow
    const arrow = document.createElement('div');
    arrow.className = 'tooltip-arrow';
    tooltip.appendChild(arrow);
    
    document.body.appendChild(tooltip);
    return tooltip;
  }

  showTooltip(element, tooltip) {
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    
    // Posicionamento inteligente
    let top = rect.top - tooltipRect.height - 10;
    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    
    // Ajusta se sair da tela
    if (top < 10) {
      top = rect.bottom + 10;
      tooltip.classList.add('tooltip-bottom');
    }
    
    if (left < 10) left = 10;
    if (left + tooltipRect.width > window.innerWidth - 10) {
      left = window.innerWidth - tooltipRect.width - 10;
    }
    
    tooltip.style.top = `${top}px`;
    tooltip.style.left = `${left}px`;
    tooltip.classList.add('opacity-100');
  }

  hideTooltip(tooltip) {
    tooltip.classList.remove('opacity-100');
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 300);
  }

  // 3. CAROUSEL PROGRESSIVO
  initProgressiveCarousel() {
    const carousels = document.querySelectorAll('[data-carousel-progressive]');
    
    carousels.forEach(carousel => {
      this.setupProgressiveCarousel(carousel);
    });
  }

  setupProgressiveCarousel(carousel) {
    const items = carousel.querySelectorAll('[data-carousel-item]');
    const indicators = carousel.querySelectorAll('[data-carousel-slide-to]');
    let currentIndex = 0;
    let autoplayInterval;

    // Auto-play
    const startAutoplay = () => {
      autoplayInterval = setInterval(() => {
        this.nextSlide(items, indicators, currentIndex);
        currentIndex = (currentIndex + 1) % items.length;
      }, 5000);
    };

    const stopAutoplay = () => {
      clearInterval(autoplayInterval);
    };

    // Event listeners
    carousel.addEventListener('mouseenter', stopAutoplay);
    carousel.addEventListener('mouseleave', startAutoplay);

    // Touch support
    let startX = 0;
    let endX = 0;

    carousel.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      stopAutoplay();
    });

    carousel.addEventListener('touchend', (e) => {
      endX = e.changedTouches[0].clientX;
      const diff = startX - endX;
      
      if (Math.abs(diff) > 50) {
        if (diff > 0) {
          // Swipe left - next
          this.nextSlide(items, indicators, currentIndex);
          currentIndex = (currentIndex + 1) % items.length;
        } else {
          // Swipe right - previous
          this.prevSlide(items, indicators, currentIndex);
          currentIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
        }
      }
      
      startAutoplay();
    });

    startAutoplay();
  }

  nextSlide(items, indicators, currentIndex) {
    const nextIndex = (currentIndex + 1) % items.length;
    this.showSlide(items, indicators, nextIndex);
  }

  prevSlide(items, indicators, currentIndex) {
    const prevIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
    this.showSlide(items, indicators, prevIndex);
  }

  showSlide(items, indicators, index) {
    items.forEach((item, i) => {
      if (i === index) {
        item.classList.remove('hidden');
        item.classList.add('animate-slide-in-right');
      } else {
        item.classList.add('hidden');
        item.classList.remove('animate-slide-in-right');
      }
    });

    indicators.forEach((indicator, i) => {
      if (i === index) {
        indicator.classList.add('bg-blue-500');
        indicator.classList.remove('bg-gray-300');
      } else {
        indicator.classList.remove('bg-blue-500');
        indicator.classList.add('bg-gray-300');
      }
    });
  }

  // 4. SCROLL INFINITO
  initInfiniteScroll() {
    const containers = document.querySelectorAll('[data-infinite-scroll]');
    
    containers.forEach(container => {
      this.setupInfiniteScroll(container);
    });
  }

  setupInfiniteScroll(container) {
    const loadMore = container.dataset.loadMore;
    const threshold = parseInt(container.dataset.threshold) || 200;
    let loading = false;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !loading) {
          loading = true;
          this.loadMoreContent(container, loadMore).then(() => {
            loading = false;
          });
        }
      });
    }, {
      rootMargin: `${threshold}px`
    });

    // Cria sentinel element
    const sentinel = document.createElement('div');
    sentinel.className = 'h-1';
    container.appendChild(sentinel);
    observer.observe(sentinel);
  }

  async loadMoreContent(container, endpoint) {
    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      
      // Adiciona novo conteúdo
      data.items.forEach(item => {
        const element = this.createContentElement(item);
        container.insertBefore(element, container.lastElementChild);
      });
      
      // Trigger lazy loading para novos elementos
      const newImages = container.querySelectorAll('img[data-src]:not([src])');
      newImages.forEach(img => {
        window.ModernComponents.instances.lazyLoader.observe(img);
      });
      
    } catch (error) {
      console.error('Erro ao carregar mais conteúdo:', error);
      window.ModernComponents.instances.notificationSystem.show(
        'Erro ao carregar mais conteúdo', 
        'error'
      );
    }
  }

  createContentElement(item) {
    const element = document.createElement('div');
    element.className = 'content-item animate-slide-in-right';
    element.innerHTML = `
      <div class="card-music p-4">
        <img data-src="${item.image}" alt="${item.title}" class="w-full h-48 object-cover rounded-lg mb-4">
        <h3 class="text-lg font-semibold mb-2">${item.title}</h3>
        <p class="text-gray-600 dark:text-gray-400">${item.description}</p>
      </div>
    `;
    return element;
  }

  // 5. TABS AVANÇADAS
  initAdvancedTabs() {
    const tabGroups = document.querySelectorAll('[data-tabs-enhanced]');
    
    tabGroups.forEach(group => {
      this.setupAdvancedTabs(group);
    });
  }

  setupAdvancedTabs(group) {
    const tabs = group.querySelectorAll('[data-tab-target]');
    const contents = group.querySelectorAll('[data-tab-content]');
    
    tabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        const target = tab.dataset.tabTarget;
        this.switchTab(tabs, contents, target);
      });
    });
  }

  switchTab(tabs, contents, target) {
    // Atualiza tabs
    tabs.forEach(tab => {
      if (tab.dataset.tabTarget === target) {
        tab.classList.add('border-blue-500', 'text-blue-600');
        tab.classList.remove('border-transparent', 'text-gray-500');
      } else {
        tab.classList.remove('border-blue-500', 'text-blue-600');
        tab.classList.add('border-transparent', 'text-gray-500');
      }
    });

    // Atualiza conteúdo
    contents.forEach(content => {
      if (content.dataset.tabContent === target) {
        content.classList.remove('hidden');
        content.classList.add('animate-slide-in-right');
      } else {
        content.classList.add('hidden');
        content.classList.remove('animate-slide-in-right');
      }
    });
  }
}

// Inicialização
document.addEventListener('DOMContentLoaded', () => {
  window.flowbiteEnhanced = new FlowbiteEnhanced();
});
