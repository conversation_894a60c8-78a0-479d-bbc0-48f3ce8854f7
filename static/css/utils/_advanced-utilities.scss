/**
 * Utilitários Avançados do TailwindCSS
 * Sistema de design expandido para IamSHIUBA
 */

/* ===== ANIMAÇÕES CUSTOMIZADAS ===== */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(var(--primary-rgb), 0.5);
  }
  50% { 
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.8), 
                0 0 30px rgba(var(--primary-rgb), 0.6);
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* ===== CLASSES UTILITÁRIAS CUSTOMIZADAS ===== */

/* Animações */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Gradientes Musicais */
.bg-music-gradient {
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 100%);
}

.bg-sound-gradient {
  background: linear-gradient(135deg, 
    #f093fb 0%, 
    #f5576c 100%);
}

.bg-beat-gradient {
  background: linear-gradient(135deg, 
    #4facfe 0%, 
    #00f2fe 100%);
}

/* Efeitos de Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Efeitos de Hover Avançados */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.6);
  transform: scale(1.02);
}

/* Botões Musicais Customizados */
.btn-play {
  @apply relative overflow-hidden bg-gradient-to-r from-red-500 to-pink-500 
         text-white font-semibold py-3 px-6 rounded-full 
         transform transition-all duration-300 hover:scale-105;
}

.btn-play::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.5s;
}

.btn-play:hover::before {
  left: 100%;
}

.btn-secondary {
  @apply bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 
         font-medium py-2 px-4 rounded-lg border border-gray-300 
         dark:border-gray-600 hover:bg-gray-300 dark:hover:bg-gray-600 
         transition-colors duration-200;
}

/* Cards Avançados */
.card-music {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg 
         border border-gray-200 dark:border-gray-700 
         overflow-hidden transition-all duration-300 
         hover:shadow-xl hover:-translate-y-1;
}

.card-playlist {
  @apply card-music relative group cursor-pointer;
}

.card-playlist::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    rgba(var(--primary-rgb), 0.1), 
    rgba(var(--secondary-rgb), 0.1));
  opacity: 0;
  transition: opacity 0.3s;
}

.card-playlist:hover::after {
  opacity: 1;
}

/* Indicadores de Status */
.status-online {
  @apply relative;
}

.status-online::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Skeleton Loading */
.skeleton {
  @apply bg-gray-200 dark:bg-gray-700 animate-pulse rounded;
}

.skeleton-text {
  @apply skeleton h-4 mb-2;
}

.skeleton-title {
  @apply skeleton h-6 mb-4;
}

.skeleton-avatar {
  @apply skeleton w-12 h-12 rounded-full;
}

/* Scrollbar Customizada */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary-rgb), 0.5) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(var(--primary-rgb), 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary-rgb), 0.7);
}

/* Responsividade Avançada */
@media (max-width: 640px) {
  .mobile-stack {
    @apply flex-col space-y-4 space-x-0;
  }
  
  .mobile-full {
    @apply w-full;
  }
  
  .mobile-center {
    @apply text-center;
  }
}

/* Estados de Interação */
.interactive {
  @apply cursor-pointer select-none transition-all duration-200;
}

.interactive:active {
  @apply scale-95;
}

.disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* Efeitos de Foco Melhorados */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 
         focus:ring-blue-500 dark:focus:ring-offset-gray-800;
}

/* Transições Suaves */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Utilitários de Layout */
.center-absolute {
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
}

.full-screen {
  @apply fixed inset-0 w-full h-full;
}

/* Efeitos de Texto */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
